{"name": "cninfo-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "vite --host 0.0.0.0 --port 3000", "build": "vite build", "preview": "vite preview --host 0.0.0.0"}, "dependencies": {"@headlessui/react": "^1.7.17", "axios": "^1.6.0", "clsx": "^2.0.0", "framer-motion": "^10.16.0", "lucide-react": "^0.294.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.4.1", "react-markdown": "^9.0.1", "rehype-highlight": "^7.0.0", "remark-gfm": "^4.0.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@vitejs/plugin-react": "^4.0.0", "autoprefixer": "^10.4.0", "postcss": "^8.4.0", "tailwindcss": "^3.3.0", "typescript": "^5.2.0", "vite": "^4.4.0"}}