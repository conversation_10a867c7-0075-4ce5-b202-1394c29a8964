'use client'

import { useState, useEffect } from 'react'
import { CheckCircle, XCircle, Loader2, Square, Clock, Play } from 'lucide-react'
import { api } from '@/lib/api'
import { Card, CardHeader, CardTitle, CardContent } from './ui/Card'
import { Button } from './ui/Button'
import { cn } from '@/lib/utils'

interface TaskProgressProps {
  taskId: string
  onComplete: (results: any) => void
}

export function TaskProgress({ taskId, onComplete }: TaskProgressProps) {
  const [status, setStatus] = useState<any>(null)
  const [isPolling, setIsPolling] = useState(true)

  useEffect(() => {
    if (!taskId || !isPolling) return

    const pollStatus = async () => {
      try {
        const response = await api.get(`/task_status/${taskId}`)
        if (response.data.success) {
          const taskStatus = response.data.data
          setStatus(taskStatus)

          if (taskStatus.status === 'completed') {
            setIsPolling(false)
            // 获取分析结果
            console.log('🎯 任务完成，获取分析结果...')
            try {
              const resultsResponse = await api.get(`/analysis_results/${taskId}`)
              console.log('📥 分析结果响应:', resultsResponse)

              if (resultsResponse.data.success) {
                console.log('✅ 分析结果获取成功:', resultsResponse.data.data)
                // 确保结果中包含task_id信息
                const resultsWithTaskId = {
                  ...resultsResponse.data.data,
                  task_id: taskId,
                  analysis_id: taskId
                }
                console.log('📤 传递给回调的结果:', resultsWithTaskId)
                onComplete(resultsWithTaskId)
              } else {
                console.error('❌ 分析结果获取失败:', resultsResponse.data.message)
              }
            } catch (error) {
              console.error('❌ 获取分析结果时出错:', error)
            }
          } else if (taskStatus.status === 'error' || taskStatus.status === 'stopped') {
            setIsPolling(false)
          }
        }
      } catch (error) {
        // 获取任务状态失败，静默处理
      }
    }

    // 立即执行一次
    pollStatus()

    // 设置轮询
    const interval = setInterval(pollStatus, 2000)

    return () => clearInterval(interval)
  }, [taskId, isPolling, onComplete])

  const handleStop = async () => {
    try {
      await api.post(`/stop_task/${taskId}`)
      setIsPolling(false)
    } catch (error) {
      // 停止任务失败，静默处理
    }
  }

  if (!status) {
    return (
      <Card className="animate-in fade-in">
        <CardContent className="py-6">
          <div className="flex items-center space-x-3">
            <Loader2 className="w-5 h-5 animate-spin text-gray-600" />
            <span className="text-gray-600">正在获取任务状态...</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  const getStatusIcon = () => {
    switch (status.status) {
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-600" />
      case 'error':
      case 'stopped':
        return <XCircle className="w-5 h-5 text-red-600" />
      default:
        return <Loader2 className="w-5 h-5 animate-spin text-blue-600" />
    }
  }

  const getStatusColor = () => {
    switch (status.status) {
      case 'completed':
        return 'text-green-600'
      case 'error':
      case 'stopped':
        return 'text-red-600'
      default:
        return 'text-blue-600'
    }
  }

  const getStatusBadge = () => {
    switch (status.status) {
      case 'completed':
        return 'badge-success'
      case 'error':
      case 'stopped':
        return 'badge-error'
      case 'running':
        return 'badge-default'
      default:
        return 'badge-default'
    }
  }

  return (
    <Card className="animate-in fade-in slide-in-from-top">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            {getStatusIcon()}
            <div>
              <CardTitle className="text-base">任务执行状态</CardTitle>
              <div className="flex items-center space-x-2 mt-1">
                <span className={cn('badge', getStatusBadge())}>
                  {status.status === 'running' && '执行中'}
                  {status.status === 'completed' && '已完成'}
                  {status.status === 'error' && '执行失败'}
                  {status.status === 'stopped' && '已停止'}
                </span>
                <span className="text-sm text-gray-500">
                  任务ID: {taskId.slice(0, 8)}...
                </span>
              </div>
            </div>
          </div>

          {status.status === 'running' && (
            <Button
              variant="danger"
              size="sm"
              onClick={handleStop}
            >
              <Square className="w-4 h-4 mr-1" />
              停止
            </Button>
          )}
        </div>
      </CardHeader>

      <CardContent>
        <div className="space-y-4">
          {/* 状态消息 */}
          <div className="p-3 bg-gray-50 rounded-lg">
            <p className={cn('text-sm font-medium', getStatusColor())}>
              {status.message}
            </p>
          </div>

          {/* 进度条 */}
          {status.status === 'running' && (
            <div className="space-y-3">
              <div className="flex justify-between items-center text-sm">
                <span className="text-gray-600">
                  步骤: {status.current_step || 0} / {status.total_steps || 0}
                </span>
                <span className="font-medium text-gray-900">
                  {status.progress || 0}%
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2.5">
                <div
                  className="bg-blue-600 h-2.5 rounded-full transition-all duration-500 ease-out"
                  style={{ width: `${status.progress || 0}%` }}
                />
              </div>
            </div>
          )}

          {/* 时间信息 */}
          {status.start_time && (
            <div className="flex items-center space-x-4 text-xs text-gray-500 pt-2 border-t border-gray-100">
              <div className="flex items-center space-x-1">
                <Play className="w-3 h-3" />
                <span>开始: {new Date(status.start_time).toLocaleString()}</span>
              </div>
              {status.end_time && (
                <div className="flex items-center space-x-1">
                  <Clock className="w-3 h-3" />
                  <span>结束: {new Date(status.end_time).toLocaleString()}</span>
                </div>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
