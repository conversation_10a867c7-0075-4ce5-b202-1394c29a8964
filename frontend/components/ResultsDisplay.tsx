'use client'

import { useState } from 'react'
import {
  Download,
  Filter,
  Eye,
  ChevronDown,
  ChevronUp,
  Search,
  SortAsc,
  SortDesc,
  FileText,
  Building2,
  Hash,
  Loader2
} from 'lucide-react'
import { Card, CardHeader, CardTitle, CardContent } from './ui/Card'
import { Button } from './ui/Button'
import { Input } from './ui/Input'
import { SimpleContextModal } from './SimpleContextModal'
import { cn } from '@/lib/utils'
import { apiMethods } from '@/lib/api'
import toast from 'react-hot-toast'

interface ResultsDisplayProps {
  results: any[]
  analysisId?: string | null
}

export function ResultsDisplay({ results, analysisId }: ResultsDisplayProps) {
  const [filters, setFilters] = useState({
    keyword: '',
    company: '',
    minCount: 0,
    hideZero: false,
  })
  const [sortBy, setSortBy] = useState<'count' | 'company' | 'keyword'>('count')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize] = useState(20)
  const [showFilters, setShowFilters] = useState(false)
  const [showContextModal, setShowContextModal] = useState(false)
  const [selectedItem, setSelectedItem] = useState<any>(null)
  const [isExporting, setIsExporting] = useState(false)

  // 查看关键词上下文
  const handleViewContext = (item: any) => {
    if (!item.analysis_id) {
      // 缺少分析ID，静默处理
      return
    }

    setSelectedItem(item)
    setShowContextModal(true)
  }

  // 过滤和排序数据
  const filteredResults = results
    .filter(item => {
      if (filters.keyword && !item.keyword.toLowerCase().includes(filters.keyword.toLowerCase())) {
        return false
      }
      if (filters.company && !item.company_name.toLowerCase().includes(filters.company.toLowerCase())) {
        return false
      }
      if (filters.minCount > 0 && item.count < filters.minCount) {
        return false
      }
      if (filters.hideZero && item.count === 0) {
        return false
      }
      return true
    })
    .sort((a, b) => {
      let aVal, bVal
      switch (sortBy) {
        case 'count':
          aVal = a.count
          bVal = b.count
          break
        case 'company':
          aVal = a.company_name
          bVal = b.company_name
          break
        case 'keyword':
          aVal = a.keyword
          bVal = b.keyword
          break
        default:
          return 0
      }
      
      if (typeof aVal === 'string') {
        aVal = aVal.toLowerCase()
        bVal = bVal.toLowerCase()
      }
      
      if (sortOrder === 'asc') {
        return aVal > bVal ? 1 : -1
      } else {
        return aVal < bVal ? 1 : -1
      }
    })

  // 分页
  const totalPages = Math.ceil(filteredResults.length / pageSize)
  const paginatedResults = filteredResults.slice(
    (currentPage - 1) * pageSize,
    currentPage * pageSize
  )

  const handleSort = (field: 'count' | 'company' | 'keyword') => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      setSortBy(field)
      setSortOrder('desc')
    }
  }

  // 导出所有结果
  const handleExportAll = async (format: 'excel' | 'csv' = 'excel') => {
    if (!analysisId) {
      toast.error('缺少分析ID，无法导出')
      return
    }

    setIsExporting(true)
    try {
      const response = await apiMethods.exportResults(analysisId)

      // 创建下载链接
      const url = window.URL.createObjectURL(new Blob([response.data]))
      const link = document.createElement('a')
      link.href = url

      const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '')
      const extension = format === 'excel' ? 'xlsx' : 'csv'
      link.download = `analysis_results_${analysisId}_${timestamp}.${extension}`

      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)

      toast.success(`${format === 'excel' ? 'Excel' : 'CSV'}文件导出成功`)
    } catch (error: any) {
      toast.error('导出失败: ' + (error.response?.data?.message || error.message))
    } finally {
      setIsExporting(false)
    }
  }

  // 导出筛选结果
  const handleExportFiltered = async (format: 'excel' | 'csv' = 'excel') => {
    if (filteredResults.length === 0) {
      toast.error('没有可导出的筛选结果')
      return
    }

    setIsExporting(true)
    try {
      const exportData = filteredResults.map(item => ({
        company_name: item.company_name || item.stock_code,
        stock_code: item.stock_code,
        file_name: item.file_name,
        keyword: item.keyword,
        count: item.count
      }))

      const response = await apiMethods.exportFilteredResults({
        data: exportData,
        filters,
        format,
        timestamp: new Date().toISOString()
      })

      // 创建下载链接
      const url = window.URL.createObjectURL(new Blob([response.data]))
      const link = document.createElement('a')
      link.href = url

      const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '')
      const extension = format === 'excel' ? 'xlsx' : 'csv'
      link.download = `filtered_results_${timestamp}.${extension}`

      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)

      toast.success(`筛选结果${format === 'excel' ? 'Excel' : 'CSV'}文件导出成功`)
    } catch (error: any) {
      toast.error('导出失败: ' + (error.response?.data?.message || error.message))
    } finally {
      setIsExporting(false)
    }
  }

  const SortIcon = ({ field }: { field: string }) => {
    if (sortBy !== field) return null
    return sortOrder === 'asc' ?
      <SortAsc className="w-4 h-4" /> :
      <SortDesc className="w-4 h-4" />
  }

  return (
    <Card className="animate-in fade-in slide-in-from-right">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="flex items-center justify-center w-10 h-10 bg-blue-100 rounded-lg">
              <FileText className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <CardTitle>分析结果</CardTitle>
              <p className="text-sm text-gray-500 mt-1">
                共找到 {filteredResults.length} 条记录
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowFilters(!showFilters)}
            >
              <Filter className="w-4 h-4 mr-1" />
              筛选
            </Button>

            {/* 导出按钮组 */}
            <div className="flex items-center space-x-1">
              <Button
                size="sm"
                onClick={() => handleExportAll('excel')}
                disabled={isExporting || !analysisId}
                title={!analysisId ? '缺少分析ID，无法导出' : '导出所有结果为Excel'}
              >
                {isExporting ? (
                  <Loader2 className="w-4 h-4 mr-1 animate-spin" />
                ) : (
                  <Download className="w-4 h-4 mr-1" />
                )}
                导出全部
              </Button>

              {filteredResults.length < results.length && (
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleExportFiltered('excel')}
                  disabled={isExporting || filteredResults.length === 0}
                  title="导出筛选结果为Excel"
                >
                  {isExporting ? (
                    <Loader2 className="w-4 h-4 mr-1 animate-spin" />
                  ) : (
                    <Download className="w-4 h-4 mr-1" />
                  )}
                  导出筛选
                </Button>
              )}
            </div>
          </div>
        </div>
      </CardHeader>

      <CardContent>
        {showFilters && (
          <div className="bg-gray-50 rounded-lg p-4 mb-6 space-y-4 animate-in fade-in slide-in-from-top">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Input
                label="关键词筛选"
                placeholder="搜索关键词"
                value={filters.keyword}
                onChange={(e) => setFilters({ ...filters, keyword: e.target.value })}
              />
              <Input
                label="公司名称"
                placeholder="搜索公司"
                value={filters.company}
                onChange={(e) => setFilters({ ...filters, company: e.target.value })}
              />
              <Input
                label="最小出现次数"
                type="number"
                min="0"
                value={filters.minCount.toString()}
                onChange={(e) => setFilters({ ...filters, minCount: parseInt(e.target.value) || 0 })}
              />
              <div className="flex items-end pb-2">
                <label className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={filters.hideZero}
                    onChange={(e) => setFilters({ ...filters, hideZero: e.target.checked })}
                    className="rounded border-gray-300 text-black focus:ring-black"
                  />
                  <span className="text-sm text-gray-700">隐藏零次记录</span>
                </label>
              </div>
            </div>
          </div>
        )}

        {/* 统计信息 */}
        <div className="flex items-center justify-between mb-4 text-sm text-gray-600">
          <div className="flex items-center space-x-4">
            <span>共 {filteredResults.length} 条结果</span>
            {filteredResults.length !== results.length && (
              <span className="text-gray-500">（已筛选，原始数据 {results.length} 条）</span>
            )}
          </div>
          <div className="text-xs text-gray-500">
            第 {currentPage} 页，共 {totalPages} 页
          </div>
        </div>

        {/* 数据表格 */}
        <div className="overflow-hidden rounded-lg border border-gray-200">
          <table className="table">
            <thead className="table-header">
              <tr>
                <th
                  className="table-header-cell cursor-pointer hover:bg-gray-100 transition-colors"
                  onClick={() => handleSort('company')}
                >
                  <div className="flex items-center space-x-2">
                    <Building2 className="w-4 h-4" />
                    <span>公司名称</span>
                    <SortIcon field="company" />
                  </div>
                </th>
                <th
                  className="table-header-cell cursor-pointer hover:bg-gray-100 transition-colors"
                  onClick={() => handleSort('keyword')}
                >
                  <div className="flex items-center space-x-2">
                    <Search className="w-4 h-4" />
                    <span>关键词</span>
                    <SortIcon field="keyword" />
                  </div>
                </th>
                <th
                  className="table-header-cell cursor-pointer hover:bg-gray-100 transition-colors"
                  onClick={() => handleSort('count')}
                >
                  <div className="flex items-center space-x-2">
                    <Hash className="w-4 h-4" />
                    <span>出现次数</span>
                    <SortIcon field="count" />
                  </div>
                </th>
                <th className="table-header-cell">操作</th>
              </tr>
            </thead>
            <tbody className="table-body">
              {paginatedResults.map((item, index) => (
                <tr key={index} className="table-row">
                  <td className="table-cell">
                    <div className="font-medium text-gray-900">
                      {item.company_name}
                    </div>
                  </td>
                  <td className="table-cell">
                    <span className="badge badge-default">
                      {item.keyword}
                    </span>
                  </td>
                  <td className="table-cell">
                    <span className={cn(
                      'badge',
                      item.count > 0 ? 'badge-success' : 'badge-default'
                    )}>
                      {item.count}
                    </span>
                  </td>
                  <td className="table-cell">
                    <div className="flex items-center space-x-2">
                      {item.count > 0 && (
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-blue-600 hover:text-blue-800"
                          onClick={() => handleViewContext(item)}
                        >
                          <Eye className="w-4 h-4 mr-1" />
                          查看上下文
                        </Button>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* 分页 */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between mt-6 pt-4 border-t border-gray-200">
            <div className="text-sm text-gray-600">
              显示第 {(currentPage - 1) * pageSize + 1} - {Math.min(currentPage * pageSize, filteredResults.length)} 条，
              共 {filteredResults.length} 条记录
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
              >
                上一页
              </Button>

              <div className="flex items-center space-x-1">
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  const page = i + 1
                  return (
                    <Button
                      key={page}
                      variant={currentPage === page ? "primary" : "ghost"}
                      size="sm"
                      onClick={() => setCurrentPage(page)}
                      className="w-8 h-8 p-0"
                    >
                      {page}
                    </Button>
                  )
                })}
                {totalPages > 5 && (
                  <>
                    <span className="text-gray-500">...</span>
                    <Button
                      variant={currentPage === totalPages ? "primary" : "ghost"}
                      size="sm"
                      onClick={() => setCurrentPage(totalPages)}
                      className="w-8 h-8 p-0"
                    >
                      {totalPages}
                    </Button>
                  </>
                )}
              </div>

              <Button
                variant="ghost"
                size="sm"
                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                disabled={currentPage === totalPages}
              >
                下一页
              </Button>
            </div>
          </div>
        )}
      </CardContent>

      {/* 上下文查看模态框 */}
      <SimpleContextModal
        isOpen={showContextModal}
        onClose={() => {
          setShowContextModal(false)
          setSelectedItem(null)
        }}
        item={selectedItem}
      />
    </Card>
  )
}
