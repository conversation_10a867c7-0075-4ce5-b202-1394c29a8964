'use client'

import { useState, useEffect } from 'react'
import { createPortal } from 'react-dom'
import { X, FileText, Search, ChevronLeft, ChevronRight } from 'lucide-react'
import { Button } from './ui/Button'
import { Badge } from './ui/Badge'
import { Loading } from './ui/Loading'
import { apiMethods } from '@/lib/api'
import toast from 'react-hot-toast'

interface SimpleContextModalProps {
  isOpen: boolean
  onClose: () => void
  item: {
    analysis_id: string
    keyword: string
    company_name: string
    stock_code: string
    file_name: string
    count: number
  } | null
}

export function SimpleContextModal({ isOpen, onClose, item }: SimpleContextModalProps) {
  const [contextData, setContextData] = useState<any>(null)
  const [loading, setLoading] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [snippetsPerPage] = useState(5) // 每页显示5个片段

  useEffect(() => {
    if (isOpen && item) {
      setCurrentPage(1) // 重置分页
      fetchContext()
    }
  }, [isOpen, item])

  const fetchContext = async () => {
    if (!item) return

    setLoading(true)
    try {
      // 获取上下文
      
      const response = await apiMethods.getKeywordContext({
        analysis_id: item.analysis_id,
        keyword: item.keyword,
        context_length: 200,
        stock_code_filter: item.stock_code,
        file_name_filter: item.file_name
      })

      // 处理上下文响应

      if (response.data.success) {
        setContextData(response.data.data)
      } else {
        toast.error(response.data.message || '获取上下文失败')
      }
    } catch (error: any) {
      toast.error('获取上下文失败: ' + (error.response?.data?.message || error.message))
    } finally {
      setLoading(false)
    }
  }

  const handleClose = () => {
    setContextData(null)
    setCurrentPage(1)
    onClose()
  }

  if (!isOpen || !item) return null

  const contexts = contextData?.contexts || []

  // 收集所有片段用于分页
  const allSnippets: Array<{snippet: string, fileName: string, fileIndex: number}> = []
  contexts.forEach((context: any, fileIndex: number) => {
    if (context.snippets && Array.isArray(context.snippets)) {
      context.snippets.forEach((snippet: string) => {
        allSnippets.push({
          snippet,
          fileName: context.file_name || '未知文件',
          fileIndex
        })
      })
    }
  })

  // 分页计算
  const totalSnippets = allSnippets.length
  const totalPages = Math.ceil(totalSnippets / snippetsPerPage)
  const startIndex = (currentPage - 1) * snippetsPerPage
  const endIndex = startIndex + snippetsPerPage
  const currentSnippets = allSnippets.slice(startIndex, endIndex)

  const modalContent = (
    <div className="fixed inset-0 z-[9999] flex items-center justify-center p-4 sm:p-6" style={{ position: 'fixed', top: 0, left: 0, right: 0, bottom: 0 }}>
      {/* 背景遮罩 */}
      <div
        className="absolute inset-0 bg-black/60 backdrop-blur-sm transition-opacity"
        onClick={handleClose}
      />

      {/* 模态框 */}
      <div
        className="relative bg-white rounded-xl shadow-2xl w-full max-w-4xl max-h-[90vh] flex flex-col z-10 border border-gray-200/50 overflow-hidden"
        onClick={(e) => e.stopPropagation()}
      >
          {/* 头部 */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gray-50 rounded-t-lg">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                关键词上下文: "{item.keyword}"
              </h3>
              <div className="flex items-center space-x-4 text-sm text-gray-600">
                <span>公司: <strong>{item.company_name}</strong></span>
                <span>文件: <strong>{item.file_name}</strong></span>
                <Badge variant="success">出现 {item.count} 次</Badge>
              </div>
            </div>
            <Button variant="ghost" size="sm" onClick={handleClose}>
              <X className="w-5 h-5" />
            </Button>
          </div>

          {/* 内容区域 */}
          <div className="flex-1 overflow-y-auto p-6">
            {loading ? (
              <div className="flex items-center justify-center py-12">
                <Loading size="lg" text="正在获取上下文..." />
              </div>
            ) : contexts.length > 0 ? (
              <div className="space-y-6">
                {/* 统计信息 */}
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <FileText className="w-5 h-5 text-blue-600" />
                      <span className="font-medium text-blue-900">
                        找到 {contexts.length} 个相关文件，总计 {totalSnippets} 个上下文片段
                      </span>
                    </div>
                    {totalPages > 1 && (
                      <div className="text-sm text-blue-700">
                        第 {currentPage} / {totalPages} 页
                      </div>
                    )}
                  </div>
                </div>

                {/* 分页上下文列表 */}
                <div className="space-y-4">
                  {currentSnippets.map((item_snippet, index) => (
                    <div key={index} className="border border-gray-200 rounded-lg overflow-hidden">
                      <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <FileText className="w-4 h-4 text-gray-600" />
                            <span className="font-medium text-gray-900">
                              {item_snippet.fileName}
                            </span>
                          </div>
                          <Badge variant="default" size="sm">
                            片段 {startIndex + index + 1}
                          </Badge>
                        </div>
                      </div>

                      <div className="p-4">
                        <div className="bg-yellow-50 border-l-4 border-yellow-400 p-3 rounded-r">
                          <div
                            className="text-sm text-gray-700 leading-relaxed"
                            dangerouslySetInnerHTML={{
                              __html: item_snippet.snippet.replace(
                                new RegExp(`(${item.keyword})`, 'gi'),
                                '<mark class="bg-yellow-200 px-1 rounded font-medium">$1</mark>'
                              )
                            }}
                          />
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {/* 分页控件 */}
                {totalPages > 1 && (
                  <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                    <div className="text-sm text-gray-600">
                      显示第 {startIndex + 1} - {Math.min(endIndex, totalSnippets)} 条，共 {totalSnippets} 条片段
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                        disabled={currentPage === 1}
                      >
                        <ChevronLeft className="w-4 h-4 mr-1" />
                        上一页
                      </Button>

                      <div className="flex items-center space-x-1">
                        {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                          let pageNum
                          if (totalPages <= 5) {
                            pageNum = i + 1
                          } else if (currentPage <= 3) {
                            pageNum = i + 1
                          } else if (currentPage >= totalPages - 2) {
                            pageNum = totalPages - 4 + i
                          } else {
                            pageNum = currentPage - 2 + i
                          }

                          return (
                            <Button
                              key={pageNum}
                              variant={currentPage === pageNum ? "default" : "ghost"}
                              size="sm"
                              onClick={() => setCurrentPage(pageNum)}
                              className="w-8 h-8 p-0"
                            >
                              {pageNum}
                            </Button>
                          )
                        })}
                      </div>

                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                        disabled={currentPage === totalPages}
                      >
                        下一页
                        <ChevronRight className="w-4 h-4 ml-1" />
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center py-12">
                <Search className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  未找到相关上下文
                </h3>
                <p className="text-gray-500">
                  关键词 "{item.keyword}" 在该文件中可能没有具体的上下文信息
                </p>
              </div>
            )}
          </div>

          {/* 底部 */}
          <div className="flex items-center justify-between p-4 border-t border-gray-200 bg-gray-50 rounded-b-lg">
            <div className="text-sm text-gray-600">
              分析ID: {item.analysis_id}
            </div>
            <Button variant="secondary" onClick={handleClose}>
              关闭
            </Button>
          </div>
        </div>
    </div>
  )

  // 使用 Portal 将弹窗渲染到 body 元素下，确保不受父容器限制
  return typeof window !== 'undefined' ? createPortal(modalContent, document.body) : null
}
