'use client'

import { useState } from 'react'
import { Card, CardHeader, CardTitle, CardContent } from './ui/Card'
import { Button } from './ui/Button'
import { Textarea } from './ui/Input'
import { MessageSquare, Database, Hash, Users } from 'lucide-react'

interface AIAnalysisProps {
  onAnalysisStart?: (content: string) => void
}

export function AIAnalysisSimple({ onAnalysisStart }: AIAnalysisProps) {
  const [analysisParams, setAnalysisParams] = useState({
    stockCodes: '',
    keywords: '',
    relatedParties: '',
    prompt: ''
  })

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 输入区域 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <MessageSquare className="w-5 h-5" />
              <span>AI分析配置</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* 股票代码输入 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Database className="w-4 h-4 inline mr-1" />
                股票代码 (每行一个)
              </label>
              <Textarea
                placeholder="例如：&#10;000001&#10;000002&#10;600000"
                value={analysisParams.stockCodes}
                onChange={(e) => setAnalysisParams(prev => ({ ...prev, stockCodes: e.target.value }))}
                className="min-h-[80px]"
              />
            </div>

            {/* 关键词输入 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Hash className="w-4 h-4 inline mr-1" />
                统计关键词 (每行一个)
              </label>
              <Textarea
                placeholder="例如：&#10;创新&#10;研发&#10;技术合作"
                value={analysisParams.keywords}
                onChange={(e) => setAnalysisParams(prev => ({ ...prev, keywords: e.target.value }))}
                className="min-h-[80px]"
              />
            </div>

            {/* 关联方输入 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Users className="w-4 h-4 inline mr-1" />
                关联方 (每行一个，选填)
              </label>
              <Textarea
                placeholder="例如：&#10;清华大学&#10;中科院&#10;华为技术"
                value={analysisParams.relatedParties}
                onChange={(e) => setAnalysisParams(prev => ({ ...prev, relatedParties: e.target.value }))}
                className="min-h-[60px]"
              />
            </div>

            {/* 分析提示输入 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                分析提示
              </label>
              <Textarea
                placeholder="请描述您希望AI如何分析这些数据..."
                value={analysisParams.prompt}
                onChange={(e) => setAnalysisParams(prev => ({ ...prev, prompt: e.target.value }))}
                className="min-h-[100px]"
              />
            </div>

            {/* 开始分析按钮 */}
            <Button 
              className="w-full"
              onClick={() => {
                console.log('开始分析:', analysisParams)
                onAnalysisStart?.(analysisParams.prompt)
              }}
            >
              开始AI分析
            </Button>
          </CardContent>
        </Card>

        {/* 结果区域 */}
        <Card>
          <CardHeader>
            <CardTitle>AI 分析结果</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col items-center justify-center py-12 text-center">
              <MessageSquare className="w-12 h-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                等待分析结果
              </h3>
              <p className="text-gray-500 max-w-sm">
                请在左侧配置分析参数并点击开始分析
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
