'use client'

import { useEffect, useRef, useState } from 'react'

interface RealTimeTextDisplayProps {
  content: string
  isStreaming?: boolean
  className?: string
}

export function RealTimeTextDisplay({ 
  content, 
  isStreaming = false, 
  className = '' 
}: RealTimeTextDisplayProps) {
  const containerRef = useRef<HTMLDivElement>(null)
  const [showCursor, setShowCursor] = useState(false)

  // 自动滚动到底部
  useEffect(() => {
    if (containerRef.current) {
      containerRef.current.scrollTop = containerRef.current.scrollHeight
    }
  }, [content])

  // 光标闪烁效果
  useEffect(() => {
    if (isStreaming) {
      setShowCursor(true)
      const interval = setInterval(() => {
        setShowCursor(prev => !prev)
      }, 500)
      return () => clearInterval(interval)
    } else {
      setShowCursor(false)
    }
  }, [isStreaming])

  return (
    <div 
      ref={containerRef}
      className={`
        bg-gray-50 border border-gray-200 rounded-lg p-4 
        font-mono text-sm whitespace-pre-wrap 
        max-h-[600px] overflow-y-auto
        ${className}
      `}
      style={{
        lineHeight: '1.6',
        wordBreak: 'break-word'
      }}
    >
      {content}
      {isStreaming && (
        <span 
          className={`
            inline-block w-2 h-4 bg-blue-500 ml-1 
            transition-opacity duration-300
            ${showCursor ? 'opacity-100' : 'opacity-0'}
          `}
          style={{ verticalAlign: 'text-top' }}
        />
      )}
    </div>
  )
}

// 带有性能监控的版本
export function RealTimeTextDisplayWithMetrics({
  content,
  isStreaming = false,
  className = ''
}: RealTimeTextDisplayProps) {
  const containerRef = useRef<HTMLDivElement>(null)
  const [showCursor, setShowCursor] = useState(false)
  const [updateCount, setUpdateCount] = useState(0)
  const lastContentLength = useRef(0)
  const startTime = useRef(Date.now())

  // 监控内容变化（不是每次渲染）
  useEffect(() => {
    if (content.length !== lastContentLength.current) {
      lastContentLength.current = content.length
      setUpdateCount(prev => prev + 1)

      // 只在开发环境输出调试信息
      if (process.env.NODE_ENV === 'development') {
        const elapsed = ((Date.now() - startTime.current) / 1000).toFixed(1)
        console.log(`📝 内容更新: ${content.length} 字符, 更新次数: ${updateCount + 1}, 耗时: ${elapsed}s`)
      }
    }
  }, [content, updateCount])

  // 自动滚动到底部
  useEffect(() => {
    if (containerRef.current) {
      const startTime = performance.now()
      containerRef.current.scrollTop = containerRef.current.scrollHeight
      const endTime = performance.now()
      
      if (endTime - startTime > 5) {
        console.warn(`⚠️ 滚动耗时过长: ${(endTime - startTime).toFixed(2)}ms`)
      }
    }
  }, [content])

  // 光标闪烁效果
  useEffect(() => {
    if (isStreaming) {
      setShowCursor(true)
      const interval = setInterval(() => {
        setShowCursor(prev => !prev)
      }, 500)
      return () => clearInterval(interval)
    } else {
      setShowCursor(false)
    }
  }, [isStreaming])

  return (
    <div className="relative">
      {/* 性能指标显示 */}
      <div className="absolute top-2 right-2 text-xs text-gray-500 bg-white px-2 py-1 rounded shadow z-10">
        字符: {content.length} | 更新: {updateCount}
      </div>
      
      <div 
        ref={containerRef}
        className={`
          bg-gray-50 border border-gray-200 rounded-lg p-4 
          font-mono text-sm whitespace-pre-wrap 
          max-h-[600px] overflow-y-auto
          ${className}
        `}
        style={{
          lineHeight: '1.6',
          wordBreak: 'break-word'
        }}
      >
        {content}
        {isStreaming && (
          <span 
            className={`
              inline-block w-2 h-4 bg-blue-500 ml-1 
              transition-opacity duration-300
              ${showCursor ? 'opacity-100' : 'opacity-0'}
            `}
            style={{ verticalAlign: 'text-top' }}
          />
        )}
      </div>
    </div>
  )
}
