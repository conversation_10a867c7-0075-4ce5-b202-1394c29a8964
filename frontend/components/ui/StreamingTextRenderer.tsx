'use client'

import { useState, useEffect, useRef, memo } from 'react'

interface StreamingTextRendererProps {
  content: string
  className?: string
  isStreaming?: boolean
  onComplete?: () => void
}

// 简单的流式文本渲染器，用于实时显示
export const StreamingTextRenderer = memo(function StreamingTextRenderer({ 
  content, 
  className = '',
  isStreaming = false,
  onComplete
}: StreamingTextRendererProps) {
  const [displayContent, setDisplayContent] = useState('')
  const [showCursor, setShowCursor] = useState(false)
  const containerRef = useRef<HTMLDivElement>(null)
  const lastContentRef = useRef('')
  const animationFrameRef = useRef<number>()

  // 当内容变化时更新显示内容
  useEffect(() => {
    if (content !== lastContentRef.current) {
      lastContentRef.current = content
      setDisplayContent(content)
      
      // 取消之前的动画帧
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
      }
      
      // 使用requestAnimationFrame优化滚动
      animationFrameRef.current = requestAnimationFrame(() => {
        if (containerRef.current) {
          containerRef.current.scrollTop = containerRef.current.scrollHeight
        }
      })
    }
  }, [content])

  // 控制光标闪烁
  useEffect(() => {
    let interval: NodeJS.Timeout
    
    if (isStreaming) {
      setShowCursor(true)
      interval = setInterval(() => {
        setShowCursor(prev => !prev)
      }, 500)
    } else {
      setShowCursor(false)
      onComplete?.()
    }
    
    return () => {
      if (interval) clearInterval(interval)
    }
  }, [isStreaming, onComplete])

  // 清理动画帧
  useEffect(() => {
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
      }
    }
  }, [])

  return (
    <div 
      ref={containerRef}
      className={`font-mono text-sm whitespace-pre-wrap ${className}`}
      style={{ 
        maxHeight: '600px', 
        overflowY: 'auto',
        padding: '16px',
        backgroundColor: '#f9fafb',
        borderRadius: '8px',
        border: '1px solid #e5e7eb'
      }}
    >
      {displayContent}
      {/* 流式输入光标 */}
      {isStreaming && (
        <span 
          className={`inline-block w-2 h-4 bg-blue-500 ml-1 transition-opacity duration-300 ${
            showCursor ? 'opacity-100' : 'opacity-0'
          }`}
          style={{ verticalAlign: 'text-top' }}
        />
      )}
    </div>
  )
})

// 带有切换功能的渲染器
interface StreamingRendererProps {
  content: string
  isStreaming?: boolean
  className?: string
}

export function StreamingRenderer({ content, isStreaming = false, className = '' }: StreamingRendererProps) {
  const [renderMode, setRenderMode] = useState<'text' | 'markdown'>('text')
  const [isComplete, setIsComplete] = useState(false)

  // 当流式完成时，切换到Markdown渲染
  useEffect(() => {
    if (!isStreaming && content && !isComplete) {
      setIsComplete(true)
      // 延迟切换到Markdown渲染，给用户一个过渡
      setTimeout(() => {
        setRenderMode('markdown')
      }, 500)
    }
  }, [isStreaming, content, isComplete])

  // 重置状态
  useEffect(() => {
    if (isStreaming) {
      setRenderMode('text')
      setIsComplete(false)
    }
  }, [isStreaming])

  if (renderMode === 'text' || isStreaming) {
    return (
      <div className={className}>
        <StreamingTextRenderer 
          content={content}
          isStreaming={isStreaming}
          onComplete={() => setIsComplete(true)}
        />
        {isComplete && !isStreaming && (
          <div className="mt-2 text-center">
            <button
              onClick={() => setRenderMode('markdown')}
              className="px-3 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
            >
              切换到Markdown渲染
            </button>
          </div>
        )}
      </div>
    )
  }

  // Markdown渲染模式
  const ReactMarkdown = require('react-markdown')
  const remarkGfm = require('remark-gfm')
  
  return (
    <div className={className}>
      <div className="prose prose-sm max-w-none p-4 bg-gray-50 rounded-lg border">
        <ReactMarkdown remarkPlugins={[remarkGfm]}>
          {content}
        </ReactMarkdown>
      </div>
      <div className="mt-2 text-center">
        <button
          onClick={() => setRenderMode('text')}
          className="px-3 py-1 text-xs bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
        >
          切换到文本模式
        </button>
      </div>
    </div>
  )
}
