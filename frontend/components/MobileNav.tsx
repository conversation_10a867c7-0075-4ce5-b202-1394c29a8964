'use client'

import {
  Bar<PERSON>hart3,
  Search,
  Bot,
  Upload
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface MobileNavProps {
  activeTab: string
  onTabChange: (tab: string) => void
}

const navigation = [
  {
    id: 'analysis',
    name: '在线分析',
    icon: BarChart3,
  },
  {
    id: 'keyword',
    name: '本地分析',
    icon: Search,
  },
  {
    id: 'ai',
    name: 'AI分析',
    icon: Bot,
  },
  {
    id: 'import',
    name: '数据导入',
    icon: Upload,
  }
]

export function MobileNav({ activeTab, onTabChange }: MobileNavProps) {

  return (
    <>




      {/* 移动端悬浮椭圆底部导航栏 */}
      <div className="lg:hidden fixed bottom-6 left-1/2 transform -translate-x-1/2 z-40 safe-area-pb float-animation">
        <div className="glass-effect rounded-full px-3 py-2">
          <div className="flex items-center justify-center space-x-4">
            {navigation.map((item) => {
              const Icon = item.icon
              const isActive = activeTab === item.id

              return (
                <button
                  key={item.id}
                  onClick={() => onTabChange(item.id)}
                  className={cn(
                    'flex flex-col items-center justify-center space-y-1 px-2 py-2 rounded-full transition-all duration-300 active:scale-90 min-w-[50px]',
                    isActive
                      ? 'text-black bg-white/60 shadow-md backdrop-blur-sm'
                      : 'text-gray-600 hover:text-gray-800 hover:bg-white/30'
                  )}
                >
                  <Icon className="w-5 h-5 flex-shrink-0" />
                  <span className="text-xs font-medium text-center leading-tight">
                    {item.name}
                  </span>
                </button>
              )
            })}
          </div>
        </div>
      </div>
    </>
  )
}
