'use client'

import {
  Bar<PERSON>hart3,
  Search,
  Bot,
  Upload
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface MobileNavProps {
  activeTab: string
  onTabChange: (tab: string) => void
}

const navigation = [
  {
    id: 'analysis',
    name: '在线分析',
    icon: BarChart3,
  },
  {
    id: 'keyword',
    name: '本地分析',
    icon: Search,
  },
  {
    id: 'ai',
    name: 'AI 分析',
    icon: Bot,
  },
  {
    id: 'import',
    name: '数据导入',
    icon: Upload,
  }
]

export function MobileNav({ activeTab, onTabChange }: MobileNavProps) {

  return (
    <>




      {/* 移动端底部导航栏 */}
      <div className="lg:hidden fixed bottom-0 left-0 right-0 bg-white/95 backdrop-blur-sm border-t border-gray-200 z-40 safe-area-pb">
        <div className="flex items-center justify-between px-2 py-2">
          {navigation.map((item) => {
            const Icon = item.icon
            const isActive = activeTab === item.id

            return (
              <button
                key={item.id}
                onClick={() => onTabChange(item.id)}
                className={cn(
                  'flex flex-col items-center justify-center space-y-1 px-2 py-2 rounded-lg transition-all duration-200 min-w-0 flex-1 active:scale-95',
                  isActive
                    ? 'text-black bg-gray-100'
                    : 'text-gray-500 hover:text-gray-700 active:bg-gray-50'
                )}
                style={{ minWidth: '60px' }}
              >
                <Icon className="w-5 h-5 flex-shrink-0" />
                <span className="text-xs font-medium text-center leading-tight whitespace-nowrap overflow-hidden text-ellipsis max-w-full">
                  {item.name}
                </span>
              </button>
            )
          })}
        </div>
      </div>
    </>
  )
}
