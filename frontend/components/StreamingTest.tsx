'use client'

import { useState, useRef } from 'react'
import { Button } from './ui/Button'
import { StreamingRenderer } from './ui/StreamingTextRenderer'
import { StreamingMarkdownDisplay } from './ui/StreamingMarkdownDisplay'
import { SimpleStreamingMarkdown } from './ui/SimpleStreamingMarkdown'
import { debugStream } from '../utils/streamingDebug'

export function StreamingTest() {
  const [content, setContent] = useState('')
  const [isStreaming, setIsStreaming] = useState(false)
  const [testMode, setTestMode] = useState<'basic' | 'markdown' | 'simple'>('simple')
  const intervalRef = useRef<NodeJS.Timeout>()

  // 模拟流式响应
  const simulateStreaming = () => {
    const mockResponse = `# 🤖 AI协同创新分析报告

## 📊 执行摘要

本次分析基于2024年年报数据，重点关注企业在**协同创新**领域的表现和发展趋势。通过深度挖掘年报内容，我们发现了以下关键洞察：

## 🔍 主要发现

### 1. 技术创新投入持续增长
- **研发支出**：同比增长 **35.2%**，达到营收的 **12.8%**
- **专利申请**：新增发明专利 **156** 项，实用新型专利 **89** 项
- **研发团队**：技术人员占比提升至 **68%**，博士学历人员增加 **40%**

### 2. 产学研合作深度拓展
- 与 **清华大学**、**中科院** 等顶尖院所建立联合实验室
- 参与国家重点研发计划项目 **3** 项
- 承担省级科技攻关项目 **8** 项

### 3. 行业生态协同效应显著
- 牵头成立行业技术联盟，汇聚上下游企业 **50+** 家
- 制定行业标准 **12** 项，其中国家标准 **3** 项
- 开放技术平台，服务合作伙伴 **200+** 家

## 📈 数据洞察

### 创新投入结构分析
\`\`\`
基础研究：25%  ████████
应用研究：45%  ██████████████████
技术开发：30%  ████████████
\`\`\`

### 合作网络分布
| 合作类型 | 数量 | 占比 | 成果转化率 |
|---------|------|------|-----------|
| 高校合作 | 15 | 35% | 78% |
| 科研院所 | 12 | 28% | 82% |
| 企业联盟 | 16 | 37% | 65% |

## 🎯 战略建议

### 短期目标（1年内）
1. **深化产学研合作**
   - 增设博士后工作站
   - 扩大联合培养研究生规模
   - 建立技术转移中心

2. **完善创新生态**
   - 设立创新基金，支持初创企业
   - 建设开放式创新平台
   - 举办行业技术峰会

### 中期规划（2-3年）
1. **国际化布局**
   - 在硅谷设立研发中心
   - 与欧洲顶尖大学建立合作
   - 参与国际标准制定

2. **数字化转型**
   - 构建AI驱动的研发平台
   - 实现全流程数字化管理
   - 建设智能制造示范工厂

## 🚀 创新亮点

> **突破性技术**：在量子计算领域取得重大进展，相关论文发表在《Nature》期刊

> **产业化成果**：新一代芯片产品性能提升300%，功耗降低50%

> **社会影响**：技术成果应用于疫情防控，服务全国200+城市

## ⚠️ 风险提示

- **技术风险**：前沿技术存在不确定性，需要持续投入
- **市场风险**：国际贸易环境变化可能影响合作
- **人才风险**：高端人才竞争激烈，需要完善激励机制

## 📋 结论与展望

企业在协同创新方面表现卓越，已形成**产学研用**深度融合的创新生态。建议继续加大投入，深化国际合作，力争在3-5年内成为行业创新引领者。

---
*📅 报告生成时间：${new Date().toLocaleString()}*
*🔍 数据来源：2024年年报及公开信息*
*⚡ 分析引擎：AI智能分析系统 v2.0*`

    setContent('')
    setIsStreaming(true)
    debugStream.start()

    let index = 0
    const chunkSize = 50 // 每次添加50个字符

    intervalRef.current = setInterval(() => {
      if (index < mockResponse.length) {
        const chunk = mockResponse.slice(index, index + chunkSize)
        setContent(prev => prev + chunk)
        debugStream.chunk(chunk, 'mock_chunk')
        index += chunkSize
      } else {
        // 流式完成
        setIsStreaming(false)
        debugStream.end()
        if (intervalRef.current) {
          clearInterval(intervalRef.current)
        }
      }
    }, 100) // 每100ms添加一块
  }

  const stopStreaming = () => {
    setIsStreaming(false)
    debugStream.end()
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
    }
  }

  const clearContent = () => {
    setContent('')
    setIsStreaming(false)
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
    }
  }

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">
          流式渲染测试
        </h1>
        
        {/* 测试模式选择 */}
        <div className="flex space-x-2 mb-4">
          <button
            onClick={() => setTestMode('basic')}
            className={`px-4 py-2 text-sm rounded-lg transition-colors ${
              testMode === 'basic'
                ? 'bg-blue-500 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            基础渲染器
          </button>
          <button
            onClick={() => setTestMode('markdown')}
            className={`px-4 py-2 text-sm rounded-lg transition-colors ${
              testMode === 'markdown'
                ? 'bg-blue-500 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            Markdown渲染器
          </button>
          <button
            onClick={() => setTestMode('simple')}
            className={`px-4 py-2 text-sm rounded-lg transition-colors ${
              testMode === 'simple'
                ? 'bg-blue-500 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            简化渲染器
          </button>
        </div>

        <div className="flex space-x-4 mb-4">
          <Button
            onClick={simulateStreaming}
            disabled={isStreaming}
            className="bg-blue-500 hover:bg-blue-600"
          >
            {isStreaming ? '流式进行中...' : '开始模拟流式响应'}
          </Button>

          <Button
            onClick={stopStreaming}
            disabled={!isStreaming}
            variant="outline"
          >
            停止流式
          </Button>

          <Button
            onClick={clearContent}
            variant="outline"
          >
            清空内容
          </Button>

          <Button
            onClick={() => debugStream.enable()}
            variant="outline"
            size="sm"
          >
            启用调试
          </Button>

          <Button
            onClick={() => debugStream.disable()}
            variant="outline"
            size="sm"
          >
            禁用调试
          </Button>
        </div>

        <div className="text-sm text-gray-600 mb-4">
          <p>状态: {isStreaming ? '🟢 流式进行中' : '🔴 已停止'}</p>
          <p>内容长度: {content.length} 字符</p>
        </div>
      </div>

      <div className="border border-gray-200 rounded-lg p-4">
        <h2 className="text-lg font-semibold mb-4">
          流式渲染结果 - {
            testMode === 'basic' ? '基础渲染器' :
            testMode === 'markdown' ? 'Markdown渲染器' :
            '简化渲染器'
          }
        </h2>

        {testMode === 'basic' ? (
          <StreamingRenderer
            content={content}
            isStreaming={isStreaming}
            autoMarkdown={false}
          />
        ) : testMode === 'markdown' ? (
          <StreamingMarkdownDisplay
            content={content}
            isStreaming={isStreaming}
            autoMarkdown={true}
          />
        ) : (
          <SimpleStreamingMarkdown
            content={content}
            isStreaming={isStreaming}
            onComplete={() => {
              console.log('流式渲染完成')
            }}
          />
        )}
      </div>

      <div className="mt-6 text-sm text-gray-500">
        <h3 className="font-medium mb-2">使用说明:</h3>
        <ul className="list-disc list-inside space-y-1">
          <li><strong>基础渲染器</strong>：传统的流式文本显示</li>
          <li><strong>Markdown渲染器</strong>：完整功能的markdown渲染器</li>
          <li><strong>简化渲染器</strong>：专为AI分析优化，无字符统计bug</li>
          <li>点击"开始模拟流式响应"开始测试</li>
          <li>观察内容是否实时更新</li>
          <li>流式完成后会自动切换到格式化显示</li>
          <li>可以手动切换显示模式（原始文本/Markdown渲染）</li>
          <li>打开浏览器控制台查看调试信息</li>
        </ul>
      </div>
    </div>
  )
}
