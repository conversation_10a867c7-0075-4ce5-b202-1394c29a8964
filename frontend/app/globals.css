@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

/* 自定义光标闪烁动画 */
@keyframes cursor-blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0.2;
  }
}

.cursor-blink {
  animation: cursor-blink 1.2s ease-in-out infinite;
}

/* 移动端专用样式 */
@media (max-width: 640px) {
  /* 减少动画以提高性能 */
  *, *::before, *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  /* 保留重要动画 */
  .cursor-blink {
    animation-duration: 1.2s !important;
    animation-iteration-count: infinite !important;
  }

  /* 优化滚动 */
  * {
    -webkit-overflow-scrolling: touch;
  }
}

/* 安全区域支持 */
.safe-area-pb {
  padding-bottom: env(safe-area-inset-bottom);
}

.safe-area-pt {
  padding-top: env(safe-area-inset-top);
}

.safe-area-pl {
  padding-left: env(safe-area-inset-left);
}

.safe-area-pr {
  padding-right: env(safe-area-inset-right);
}

@layer base {
  * {
    @apply border-border;
  }

  html {
    font-family: 'Inter', system-ui, sans-serif;
    scroll-behavior: smooth;
    height: 100%;
  }

  body {
    @apply bg-background text-foreground antialiased;
    font-feature-settings: "rlig" 1, "calt" 1;
    height: 100%;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
    /* 移动端触摸优化 */
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
  }

  /* 允许文本选择的元素 */
  input, textarea, [contenteditable] {
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
  }

  /* 自定义滚动条 */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-gray-100;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400;
  }
}

@layer components {
  /* 按钮组件 */
  .btn {
    @apply inline-flex items-center justify-center rounded-lg text-xs sm:text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50;
  }

  .btn-primary {
    @apply btn bg-black text-white hover:bg-gray-800 active:bg-gray-900 shadow-sm;
  }

  .btn-secondary {
    @apply btn bg-white text-gray-900 border border-gray-200 hover:bg-gray-50 active:bg-gray-100 shadow-sm;
  }

  .btn-ghost {
    @apply btn text-gray-600 hover:text-gray-900 hover:bg-gray-100 active:bg-gray-200;
  }

  .btn-danger {
    @apply btn bg-red-600 text-white hover:bg-red-700 active:bg-red-800 shadow-sm;
  }

  .btn-sm {
    @apply h-7 sm:h-8 px-2 sm:px-3 text-xs;
  }

  .btn-md {
    @apply h-9 sm:h-10 px-3 sm:px-4 py-2;
  }

  .btn-lg {
    @apply h-10 sm:h-12 px-4 sm:px-6 text-sm sm:text-base;
  }

  /* 输入组件 */
  .input {
    @apply flex h-9 sm:h-10 w-full rounded-lg border border-gray-200 bg-white px-2 sm:px-3 py-2 text-xs sm:text-sm ring-offset-background file:border-0 file:bg-transparent file:text-xs sm:file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-black focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-200;
  }

  .textarea {
    @apply input min-h-[60px] sm:min-h-[80px] resize-none;
  }

  .select {
    @apply input cursor-pointer;
  }

  /* 卡片组件 */
  .card {
    @apply rounded-xl border border-gray-200 bg-white shadow-sm transition-all duration-200;
  }

  .card-hover {
    @apply card hover:shadow-md hover:border-gray-300;
  }

  .card-header {
    @apply flex flex-col space-y-1.5 p-3 pb-2 sm:p-4 sm:pb-3 lg:p-6 lg:pb-4;
  }

  .card-title {
    @apply text-base sm:text-lg font-semibold leading-none tracking-tight text-gray-900;
  }

  .card-description {
    @apply text-xs sm:text-sm text-gray-600;
  }

  .card-content {
    @apply p-3 pt-0 sm:p-4 sm:pt-0 lg:p-6 lg:pt-0;
  }

  .card-footer {
    @apply flex items-center p-6 pt-0;
  }

  /* 导航组件 */
  .nav-item {
    @apply flex items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium text-gray-600 transition-all duration-200 hover:text-gray-900 hover:bg-gray-100 active:bg-gray-200;
  }

  .nav-item-active {
    @apply nav-item bg-gray-100 text-gray-900;
  }

  /* 表格组件 */
  .table {
    @apply w-full caption-bottom text-sm;
  }

  .table-header {
    @apply border-b border-gray-200;
  }

  .table-header-cell {
    @apply h-10 sm:h-12 px-2 sm:px-4 text-left align-middle font-medium text-gray-600 text-xs sm:text-sm;
  }

  .table-body {
    @apply [&_tr:last-child]:border-0;
  }

  .table-row {
    @apply border-b border-gray-100 transition-colors hover:bg-gray-50;
  }

  .table-cell {
    @apply p-2 sm:p-4 align-middle text-gray-900 text-xs sm:text-sm;
    /* 移动端允许换行 */
    white-space: normal;
    word-break: break-word;
  }

  /* 移动端表格优化 */
  @media (max-width: 640px) {
    .table {
      @apply text-xs;
    }

    .table-header-cell {
      @apply h-8 px-1 py-2 text-xs;
    }

    .table-cell {
      @apply p-1 text-xs;
      max-width: 100px;
    }
  }

  /* 徽章组件 */
  .badge {
    @apply inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium transition-colors;
  }

  .badge-default {
    @apply badge bg-gray-100 text-gray-800;
  }

  .badge-success {
    @apply badge bg-green-100 text-green-800;
  }

  .badge-warning {
    @apply badge bg-yellow-100 text-yellow-800;
  }

  .badge-error {
    @apply badge bg-red-100 text-red-800;
  }

  /* 分隔符 */
  .separator {
    @apply shrink-0 bg-gray-200;
  }

  .separator-horizontal {
    @apply separator h-[1px] w-full;
  }

  .separator-vertical {
    @apply separator h-full w-[1px];
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* 动画工具类 */
  .animate-in {
    animation-duration: 0.2s;
    animation-fill-mode: both;
  }

  .animate-out {
    animation-duration: 0.15s;
    animation-fill-mode: both;
  }

  .fade-in {
    animation-name: fadeIn;
  }

  .fade-out {
    animation-name: fadeOut;
  }

  .slide-in-from-top {
    animation-name: slideInFromTop;
  }

  .slide-in-from-bottom {
    animation-name: slideInFromBottom;
  }

  .slide-in-from-left {
    animation-name: slideInFromLeft;
  }

  .slide-in-from-right {
    animation-name: slideInFromRight;
  }

  .zoom-in {
    animation-name: zoomIn;
  }

  .zoom-out {
    animation-name: zoomOut;
  }
}

/* 动画关键帧 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeOut {
  from { opacity: 1; }
  to { opacity: 0; }
}

@keyframes slideInFromTop {
  from { transform: translateY(-10px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes slideInFromBottom {
  from { transform: translateY(10px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes slideInFromLeft {
  from { transform: translateX(-10px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes slideInFromRight {
  from { transform: translateX(10px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes zoomIn {
  from { transform: scale(0.95); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

@keyframes zoomOut {
  from { transform: scale(1); opacity: 1; }
  to { transform: scale(0.95); opacity: 0; }
}
