/**
 * 本地缓存工具
 * 用于缓存用户输入的分析参数
 */

const CACHE_PREFIX = 'cninfo_analysis_'
const CACHE_EXPIRY = 7 * 24 * 60 * 60 * 1000 // 7天过期

interface CacheItem<T> {
  data: T
  timestamp: number
  expiry: number
}

class CacheManager {
  /**
   * 设置缓存
   */
  set<T>(key: string, data: T, customExpiry?: number): void {
    try {
      const expiry = customExpiry || CACHE_EXPIRY
      const cacheItem: CacheItem<T> = {
        data,
        timestamp: Date.now(),
        expiry: Date.now() + expiry
      }
      localStorage.setItem(CACHE_PREFIX + key, JSON.stringify(cacheItem))
    } catch (error) {
      console.warn('缓存设置失败:', error)
    }
  }

  /**
   * 获取缓存
   */
  get<T>(key: string): T | null {
    try {
      const cached = localStorage.getItem(CACHE_PREFIX + key)
      if (!cached) return null

      const cacheItem: CacheItem<T> = JSON.parse(cached)
      
      // 检查是否过期
      if (Date.now() > cacheItem.expiry) {
        this.remove(key)
        return null
      }

      return cacheItem.data
    } catch (error) {
      console.warn('缓存获取失败:', error)
      return null
    }
  }

  /**
   * 删除缓存
   */
  remove(key: string): void {
    try {
      localStorage.removeItem(CACHE_PREFIX + key)
    } catch (error) {
      console.warn('缓存删除失败:', error)
    }
  }

  /**
   * 清空所有缓存
   */
  clear(): void {
    try {
      const keys = Object.keys(localStorage)
      keys.forEach(key => {
        if (key.startsWith(CACHE_PREFIX)) {
          localStorage.removeItem(key)
        }
      })
    } catch (error) {
      console.warn('缓存清空失败:', error)
    }
  }

  /**
   * 获取所有缓存键
   */
  getAllKeys(): string[] {
    try {
      const keys = Object.keys(localStorage)
      return keys
        .filter(key => key.startsWith(CACHE_PREFIX))
        .map(key => key.replace(CACHE_PREFIX, ''))
    } catch (error) {
      console.warn('获取缓存键失败:', error)
      return []
    }
  }

  /**
   * 检查缓存是否存在且未过期
   */
  has(key: string): boolean {
    return this.get(key) !== null
  }

  /**
   * 获取缓存大小（字节）
   */
  getSize(): number {
    try {
      let size = 0
      const keys = Object.keys(localStorage)
      keys.forEach(key => {
        if (key.startsWith(CACHE_PREFIX)) {
          const value = localStorage.getItem(key)
          if (value) {
            size += key.length + value.length
          }
        }
      })
      return size
    } catch (error) {
      console.warn('获取缓存大小失败:', error)
      return 0
    }
  }
}

// 创建全局缓存实例
export const cache = new CacheManager()

// 预定义的缓存键
export const CACHE_KEYS = {
  // 在线分析
  ONLINE_ANALYSIS: 'online_analysis_params',

  // 本地关键词分析
  LOCAL_ANALYSIS: 'local_analysis_params',

  // AI分析
  AI_ANALYSIS: 'ai_analysis_params',
  AI_MODEL_CONFIG: 'ai_model_config',
  AI_ANALYSIS_HISTORY: 'ai_analysis_history',

  // 数据导入
  DATA_IMPORT: 'data_import_params',

  // 用户偏好
  USER_PREFERENCES: 'user_preferences'
} as const

// 分析参数接口
export interface OnlineAnalysisParams {
  stockCodes: string
  keywords: string
  relatedParties: string
  startDate: string
  endDate: string
}

export interface LocalAnalysisParams {
  stockCodes: string
  keywords: string
  relatedParties: string
  selectedYear?: string
}

export interface AIAnalysisParams {
  stockCodes: string
  keywords: string
  relatedParties: string
  prompt: string
}

export interface ModelConfig {
  useDefault: boolean
  apiKey: string
  baseUrl: string
  model: string
}

export interface AIAnalysisHistoryItem {
  id: number
  params: AIAnalysisParams
  result: string
  timestamp: Date
  duration: number
}

export interface AIAnalysisHistoryItem {
  id: number
  params: AIAnalysisParams
  result: string
  timestamp: Date
  duration?: number
}

// 缓存工具函数
export const cacheUtils = {
  /**
   * 保存在线分析参数
   */
  saveOnlineAnalysisParams: (params: OnlineAnalysisParams) => {
    cache.set(CACHE_KEYS.ONLINE_ANALYSIS, params)
  },

  /**
   * 获取在线分析参数
   */
  getOnlineAnalysisParams: (): OnlineAnalysisParams | null => {
    return cache.get<OnlineAnalysisParams>(CACHE_KEYS.ONLINE_ANALYSIS)
  },

  /**
   * 保存本地分析参数
   */
  saveLocalAnalysisParams: (params: LocalAnalysisParams) => {
    cache.set(CACHE_KEYS.LOCAL_ANALYSIS, params)
  },

  /**
   * 获取本地分析参数
   */
  getLocalAnalysisParams: (): LocalAnalysisParams | null => {
    return cache.get<LocalAnalysisParams>(CACHE_KEYS.LOCAL_ANALYSIS)
  },

  /**
   * 保存AI分析参数
   */
  saveAIAnalysisParams: (params: AIAnalysisParams) => {
    cache.set(CACHE_KEYS.AI_ANALYSIS, params)
  },

  /**
   * 获取AI分析参数
   */
  getAIAnalysisParams: (): AIAnalysisParams | null => {
    return cache.get<AIAnalysisParams>(CACHE_KEYS.AI_ANALYSIS)
  },

  /**
   * 保存AI模型配置
   */
  saveModelConfig: (config: ModelConfig) => {
    cache.set(CACHE_KEYS.AI_MODEL_CONFIG, config)
  },

  /**
   * 获取AI模型配置
   */
  getModelConfig: (): ModelConfig | null => {
    return cache.get<ModelConfig>(CACHE_KEYS.AI_MODEL_CONFIG)
  },

  /**
   * 保存AI分析历史记录
   */
  saveAIAnalysisHistory: (historyItem: AIAnalysisHistoryItem) => {
    const history = cache.get<AIAnalysisHistoryItem[]>(CACHE_KEYS.AI_ANALYSIS_HISTORY) || []
    // 添加新记录到开头，保留最近50条
    const updatedHistory = [historyItem, ...history].slice(0, 50)
    cache.set(CACHE_KEYS.AI_ANALYSIS_HISTORY, updatedHistory)
  },

  /**
   * 获取AI分析历史记录
   */
  getAIAnalysisHistory: (): AIAnalysisHistoryItem[] => {
    return cache.get<AIAnalysisHistoryItem[]>(CACHE_KEYS.AI_ANALYSIS_HISTORY) || []
  },

  /**
   * 清空AI分析历史记录
   */
  clearAIAnalysisHistory: () => {
    cache.remove(CACHE_KEYS.AI_ANALYSIS_HISTORY)
  },

  /**
   * 清空所有分析参数缓存
   */
  clearAnalysisCache: () => {
    cache.remove(CACHE_KEYS.ONLINE_ANALYSIS)
    cache.remove(CACHE_KEYS.LOCAL_ANALYSIS)
    cache.remove(CACHE_KEYS.AI_ANALYSIS)
  },

  /**
   * 获取缓存统计信息
   */
  getCacheStats: () => {
    return {
      size: cache.getSize(),
      keys: cache.getAllKeys(),
      hasOnlineAnalysis: cache.has(CACHE_KEYS.ONLINE_ANALYSIS),
      hasLocalAnalysis: cache.has(CACHE_KEYS.LOCAL_ANALYSIS),
      hasAIAnalysis: cache.has(CACHE_KEYS.AI_ANALYSIS),
      hasModelConfig: cache.has(CACHE_KEYS.AI_MODEL_CONFIG)
    }
  }
}

export default cache
