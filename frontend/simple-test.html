<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单流式测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px;
            font-size: 16px;
        }
        button:hover {
            background: #0056b3;
        }
        .content {
            border: 1px solid #ccc;
            border-radius: 4px;
            padding: 15px;
            min-height: 200px;
            background: #f9f9f9;
            white-space: pre-wrap;
            font-family: monospace;
            margin: 20px 0;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            background: #e9ecef;
        }
    </style>
</head>
<body>
    <h1>简单流式测试</h1>
    
    <button onclick="testBasic()">基础测试</button>
    <button onclick="testAPI()">API测试</button>
    <button onclick="clearAll()">清空</button>
    
    <div id="status" class="status">准备就绪</div>
    <div id="content" class="content">等待测试...</div>

    <script>
        function log(message) {
            console.log(message);
            document.getElementById('status').textContent = message;
        }
        
        function testBasic() {
            log('开始基础测试');
            const content = document.getElementById('content');
            content.innerHTML = '基础测试成功！时间: ' + new Date().toLocaleTimeString();
        }
        
        function clearAll() {
            log('清空内容');
            document.getElementById('content').innerHTML = '等待测试...';
        }
        
        async function testAPI() {
            log('开始API测试');
            const content = document.getElementById('content');
            
            try {
                // 首先测试简单的GET请求
                const response = await fetch('http://localhost:5000/api/test', {
                    method: 'GET'
                });
                
                if (response.ok) {
                    const data = await response.text();
                    content.innerHTML = 'API连接成功: ' + data;
                    log('API连接成功');
                } else {
                    throw new Error('HTTP ' + response.status);
                }
                
            } catch (error) {
                content.innerHTML = 'API连接失败: ' + error.message;
                log('API连接失败: ' + error.message);
                
                // 如果API失败，测试流式AI分析
                setTimeout(() => testStreamingAI(), 2000);
            }
        }
        
        async function testStreamingAI() {
            log('测试流式AI分析');
            const content = document.getElementById('content');
            
            try {
                const response = await fetch('http://localhost:5000/api/ai_analysis_stream', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        stock_codes: '000001',
                        keywords: '创新',
                        related_parties: '',
                        prompt: '简单测试'
                    })
                });
                
                if (!response.ok) {
                    throw new Error('HTTP ' + response.status);
                }
                
                const reader = response.body?.getReader();
                if (!reader) {
                    throw new Error('无法获取响应流');
                }
                
                let result = '';
                content.innerHTML = '开始接收流式数据...\n';
                
                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;
                    
                    const chunk = new TextDecoder().decode(value);
                    result += chunk;
                    content.innerHTML = '接收到数据:\n' + result;
                    
                    // 滚动到底部
                    content.scrollTop = content.scrollHeight;
                }
                
                log('流式数据接收完成');
                
            } catch (error) {
                content.innerHTML = '流式测试失败: ' + error.message;
                log('流式测试失败: ' + error.message);
            }
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('页面加载完成');
        });
    </script>
</body>
</html>
