<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>流式Markdown测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .controls {
            margin-bottom: 20px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .status.streaming {
            background: #d4edda;
            color: #155724;
        }
        .status.complete {
            background: #cce7ff;
            color: #004085;
        }
        .content {
            border: 1px solid #ccc;
            border-radius: 4px;
            padding: 15px;
            min-height: 200px;
            background: #f9f9f9;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .cursor {
            display: inline-block;
            width: 2px;
            height: 16px;
            background: #007bff;
            animation: blink 1s infinite;
        }
        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }
    </style>
</head>
<body>
    <h1>流式Markdown渲染测试</h1>
    
    <div class="container">
        <h2>测试控制</h2>
        <div class="controls">
            <button onclick="testStreamingMarkdown()">测试流式Markdown</button>
            <button onclick="testStreamingAPI()">测试后端API</button>
            <button onclick="clearContent()">清空内容</button>
        </div>
        
        <div id="status" class="status">准备就绪</div>
        
        <h3>显示内容:</h3>
        <div id="content" class="content">等待测试...</div>
    </div>

    <script>
        let isStreaming = false;
        let streamingInterval = null;
        
        const statusEl = document.getElementById('status');
        const contentEl = document.getElementById('content');
        
        // 测试markdown内容
        const testMarkdownContent = `# AI分析报告

## 1. 企业协同创新分析

### 1.1 创新合作概况
根据年报数据分析，以下企业在协同创新方面表现突出：

- **企业A**: 与多家高校建立产学研合作关系
- **企业B**: 参与国家重大科技专项
- **企业C**: 建立了完善的创新生态体系

### 1.2 关键技术领域

#### 人工智能
- 机器学习算法优化
- 深度学习模型应用
- 自然语言处理技术

#### 新材料
- 纳米材料研发
- 复合材料应用
- 环保材料创新

### 1.3 合作模式分析

| 合作类型 | 企业数量 | 投资规模 | 成果转化率 |
|---------|---------|---------|-----------|
| 产学研合作 | 15家 | 2.3亿元 | 78% |
| 企业联盟 | 8家 | 1.8亿元 | 65% |
| 国际合作 | 5家 | 3.2亿元 | 82% |

## 2. 创新投入分析

### 2.1 研发投入趋势
```
2021年: 1.2亿元
2022年: 1.5亿元  
2023年: 1.8亿元
2024年: 2.1亿元
```

### 2.2 人才队伍建设
- 博士学位人员: 120人
- 硕士学位人员: 350人
- 海外引进人才: 45人

## 3. 创新成果

> 通过深入分析各企业年报，我们发现协同创新已成为企业发展的重要驱动力。

### 3.1 专利申请情况
- 发明专利: 156项
- 实用新型: 89项
- 外观设计: 23项

### 3.2 技术转化成果
1. **新产品开发**: 成功推出15款创新产品
2. **工艺改进**: 优化生产工艺8项
3. **标准制定**: 参与行业标准制定3项

## 4. 发展建议

### 4.1 加强合作深度
建议企业进一步深化与高校、科研院所的合作关系，建立长期稳定的合作机制。

### 4.2 完善创新体系
- 建立完善的创新管理体系
- 加强知识产权保护
- 优化创新激励机制

### 4.3 拓展国际合作
积极参与国际科技合作项目，引进先进技术和管理经验。

---

**分析结论**: 企业协同创新能力不断提升，但仍需在合作深度、体系完善等方面持续改进。`;

        function updateStatus(message, type = '') {
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
        }
        
        function clearContent() {
            contentEl.innerHTML = '等待测试...';
            updateStatus('准备就绪');
            isStreaming = false;
            if (streamingInterval) {
                clearInterval(streamingInterval);
                streamingInterval = null;
            }
        }
        
        function testStreamingMarkdown() {
            if (isStreaming) {
                updateStatus('测试正在进行中...');
                return;
            }
            
            clearContent();
            isStreaming = true;
            updateStatus('开始流式渲染测试...', 'streaming');
            
            let currentIndex = 0;
            const chunkSize = 5; // 每次显示5个字符
            
            contentEl.innerHTML = '';
            
            streamingInterval = setInterval(() => {
                if (currentIndex >= testMarkdownContent.length) {
                    // 流式完成
                    isStreaming = false;
                    clearInterval(streamingInterval);
                    streamingInterval = null;
                    updateStatus('流式渲染完成', 'complete');
                    
                    // 移除光标
                    const cursor = contentEl.querySelector('.cursor');
                    if (cursor) {
                        cursor.remove();
                    }
                    return;
                }
                
                // 添加新的字符块
                const chunk = testMarkdownContent.slice(currentIndex, currentIndex + chunkSize);
                currentIndex += chunkSize;
                
                // 移除旧光标
                const oldCursor = contentEl.querySelector('.cursor');
                if (oldCursor) {
                    oldCursor.remove();
                }
                
                // 添加新内容和光标
                const currentContent = testMarkdownContent.slice(0, currentIndex);
                contentEl.innerHTML = currentContent + '<span class="cursor"></span>';
                
                // 滚动到底部
                contentEl.scrollTop = contentEl.scrollHeight;
                
            }, 50); // 每50ms更新一次
        }
        
        async function testStreamingAPI() {
            if (isStreaming) {
                updateStatus('测试正在进行中...');
                return;
            }
            
            clearContent();
            isStreaming = true;
            updateStatus('测试后端API流式响应...', 'streaming');
            
            try {
                const response = await fetch('http://localhost:5000/api/ai_analysis_stream', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        stock_codes: '000001',
                        keywords: '创新\n合作',
                        related_parties: '',
                        prompt: '请分析企业的协同创新情况'
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const reader = response.body?.getReader();
                if (!reader) {
                    throw new Error('无法获取响应流');
                }
                
                let accumulatedResult = '';
                contentEl.innerHTML = '';
                
                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;
                    
                    const chunk = new TextDecoder().decode(value);
                    const lines = chunk.split('\n');
                    
                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            try {
                                const data = JSON.parse(line.slice(6));
                                if (data.type === 'ai_chunk' && data.data) {
                                    accumulatedResult += data.data;
                                    
                                    // 移除旧光标
                                    const oldCursor = contentEl.querySelector('.cursor');
                                    if (oldCursor) {
                                        oldCursor.remove();
                                    }
                                    
                                    // 更新内容和光标
                                    contentEl.innerHTML = accumulatedResult + '<span class="cursor"></span>';
                                    contentEl.scrollTop = contentEl.scrollHeight;
                                    
                                } else if (data.type === 'complete') {
                                    isStreaming = false;
                                    updateStatus('API流式响应完成', 'complete');
                                    
                                    // 移除光标
                                    const cursor = contentEl.querySelector('.cursor');
                                    if (cursor) {
                                        cursor.remove();
                                    }
                                } else if (data.type === 'error') {
                                    throw new Error(data.message);
                                }
                            } catch (e) {
                                console.warn('解析流数据失败:', line, e);
                            }
                        }
                    }
                }
                
            } catch (error) {
                isStreaming = false;
                updateStatus(`API测试失败: ${error.message}`, 'error');
                contentEl.innerHTML = `错误: ${error.message}`;
            }
        }
    </script>
</body>
</html>
